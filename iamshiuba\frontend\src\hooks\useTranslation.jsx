import { useState, useEffect, useContext, createContext } from 'react'

// Translation Context
const TranslationContext = createContext()

// Translation Provider Component
export const TranslationProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('pt-BR')
  const [translations, setTranslations] = useState({})
  const [isLoading, setIsLoading] = useState(false)
  const [cachedTranslations, setCachedTranslations] = useState({})

  const supportedLanguages = ['en-US', 'pt-BR', 'jp-JP', 'ru-RU', 'hi-IN', 'zh-CN']
  const defaultLanguage = 'pt-BR'

  // Load translations for a specific language
  const loadTranslations = async (language) => {
    // Validate language code
    if (!supportedLanguages.includes(language)) {
      console.warn(`Unsupported language: ${language}, falling back to ${defaultLanguage}`)
      language = defaultLanguage
    }

    // Return cached translations if available
    if (cachedTranslations[language]) {
      return cachedTranslations[language]
    }

    try {
      setIsLoading(true)

      // Fetch translations with timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000)

      const response = await fetch(`/translations/${language}.json`, {
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`Error loading translations for ${language}: ${response.status}`)
      }

      const data = await response.json()

      // Cache the translations
      setCachedTranslations(prev => ({
        ...prev,
        [language]: data
      }))

      return data
    } catch (error) {
      console.error(`Translation loading error for ${language}:`, error)

      // If timeout or network error, try to get from cache or use default
      if (error.name === 'AbortError' || error.name === 'TypeError') {
        console.warn('Network issue when loading translations, trying fallback...')

        // Try to get from localStorage cache
        try {
          const cachedData = localStorage.getItem(`translations_${language}`)
          if (cachedData) {
            const parsedData = JSON.parse(cachedData)
            setCachedTranslations(prev => ({
              ...prev,
              [language]: parsedData
            }))
            return parsedData
          }
        } catch (e) {
          console.error('Error reading from cache:', e)
        }
      }

      // If all else fails and we're not already trying the default, use default language
      if (language !== defaultLanguage) {
        console.warn(`Falling back to ${defaultLanguage} translations`)
        return loadTranslations(defaultLanguage)
      }

      // If even the default fails, return an empty object to prevent further errors
      return {}
    } finally {
      setIsLoading(false)
    }
  }

  // Set the language and translate the page
  const setLanguage = async (language) => {
    if (isLoading) return // Prevent multiple simultaneous translation attempts

    try {
      console.log(`Attempting to set language to: ${language}`)
      const translationData = await loadTranslations(language)
      if (!translationData || Object.keys(translationData).length === 0) {
        throw new Error('No translations available')
      }

      setCurrentLanguage(language)
      setTranslations(translationData)

      // Update localStorage and HTML lang attribute
      localStorage.setItem('selectedLanguage', language)
      document.documentElement.setAttribute('lang', language)

      // Store translations in localStorage for offline use
      try {
        localStorage.setItem(`translations_${language}`, JSON.stringify(translationData))
      } catch (e) {
        console.warn('Could not cache translations in localStorage:', e)
      }

      // Update page title
      const currentPath = window.location.pathname.split('/')[1] || 'index'
      let titleKey = currentPath
      if (currentPath === 'idbadmin') {
        titleKey = 'idbadmin_dashboard'
      }

      document.title = `IamSHIUBA - ${
        translationData.title?.[titleKey] || 'IamSHIUBA'
      }`

      // Dispatch event for other components to react to language change
      window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language } }))

    } catch (error) {
      console.error('Error setting language:', error)
    }
  }

  // Get translation for a specific key
  const translate = (key) => {
    if (!key) return ''
    
    // Handle nested keys (e.g., 'title.index')
    const keys = key.split('.')
    let value = translations
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return key // Return key if translation not found
      }
    }
    
    return typeof value === 'string' ? value : key
  }

  // Initialize with saved language or default
  useEffect(() => {
    const savedLanguage = localStorage.getItem('selectedLanguage') || defaultLanguage
    setLanguage(savedLanguage)
  }, [])

  const value = {
    currentLanguage,
    translations,
    isLoading,
    supportedLanguages,
    setLanguage,
    translate,
    t: translate // Shorthand alias
  }

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  )
}

// Custom hook to use translation
export const useTranslation = () => {
  const context = useContext(TranslationContext)
  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider')
  }
  return context
}

export default useTranslation
