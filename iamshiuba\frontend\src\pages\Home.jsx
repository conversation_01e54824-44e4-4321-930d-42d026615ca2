import { useState, useEffect, useRef } from 'react'
import { Link } from 'react-router-dom'
import { playlistService } from '../services/playlistService'
import { updateService } from '../services/updateService'
import LoadingSpinner from '../components/LoadingSpinner'
import { useTranslation } from '../hooks/useTranslation.jsx'
import { useCounterAnimation } from '../hooks/useCounterAnimation'

const Home = () => {
  const [highlightPlaylist, setHighlightPlaylist] = useState(null)
  const [recentUpdates, setRecentUpdates] = useState([])
  const [loading, setLoading] = useState(true)
  const [highlightLoading, setHighlightLoading] = useState(false)
  const [highlightError, setHighlightError] = useState(null)
  const [showHighlightVideo, setShowHighlightVideo] = useState(false)

  const { t } = useTranslation()
  const heroImageRef = useRef(null)

  // Counter animation setup
  const statsRef = useRef(null)
  const { startAnimation } = useCounterAnimation()

  useEffect(() => {
    const fetchData = async () => {
      try {
        setHighlightLoading(true)
        const [playlistResponse, updatesResponse] = await Promise.all([
          playlistService.getHighlightPlaylist().catch(() => ({ highlight: null })),
          updateService.getUpdates({ limit: 3, order: 'DESC' }).catch(() => ({ updates: [] }))
        ])

        setHighlightPlaylist(playlistResponse.highlight)
        setRecentUpdates(updatesResponse.updates || [])
      } catch (error) {
        console.error('Error fetching home data:', error)
        setHighlightError('Failed to load content')
      } finally {
        setLoading(false)
        setHighlightLoading(false)
      }
    }

    fetchData()
  }, [])

  // Initialize counter animations when stats section comes into view
  useEffect(() => {
    if (!loading && statsRef.current) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              startAnimation(statsRef.current)
              observer.unobserve(entry.target)
            }
          })
        },
        { threshold: 0.1 }
      )

      observer.observe(statsRef.current)
      return () => observer.disconnect()
    }
  }, [loading, startAnimation])

  // Image optimization effect
  useEffect(() => {
    if (heroImageRef.current) {
      const img = heroImageRef.current
      const dataSrc = img.getAttribute('data-src')
      if (dataSrc) {
        img.src = dataSrc
        img.removeAttribute('data-src')
      }
    }
  }, [])

  const retryHighlights = async () => {
    setHighlightError(null)
    setHighlightLoading(true)
    try {
      const response = await playlistService.getHighlightPlaylist()
      setHighlightPlaylist(response.highlight)
    } catch (error) {
      setHighlightError('Failed to load highlights')
    } finally {
      setHighlightLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div id="home" className="justify-center items-center">
      {/* Hero Section - matching original template structure */}
      <section className="hero-section animate-[fade-in_1s_ease-in-out] py-16 overflow-hidden mx-auto md:max-w-[80%]">
        <div className="hero-wrapper flex flex-col lg:flex-row items-center">
          <div className="hero-content mb-2.5">
            <h1 className="text-4xl md:text-6xl font-bold" data-translate="greeting">
              {t('greeting')}
            </h1>
            <p className="text-lg mt-4 text-start">
              <span dangerouslySetInnerHTML={{ __html: t('mainMsg') }} />
              <br />
              <span dangerouslySetInnerHTML={{ __html: t('subMsg') }} />
            </p>
            <div className="hero-buttons mt-10 flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-4">
              <Link to="/streaming" className="hBtn inline-flex items-center">
                <i className="fas fa-play mr-2"></i>
                <span>{t('startListening')}</span>
              </Link>
              <Link to="/about" className="hBtn inline-flex items-center">
                <i className="fas fa-info-circle mr-2"></i>
                <span>{t('learnMore')}</span>
              </Link>
            </div>
          </div>
          <div className="floating-image animate-[float_6s_ease-in-out_infinite] w-full md:w-1/2 mx-auto mt-30">
            <img
              ref={heroImageRef}
              data-src="/img/iamshiuba_web.svg"
              alt="IamSHIUBA Artist Logo"
              width="500"
              height="500"
              className="hero-image"
            />
          </div>
        </div>
      </section>

      {/* Highlight Section - matching original template structure */}
      <section id="highlight-section" className="md:max-w-[80%] lg:max-w-[60%] mx-auto my-15">
        <div id="featured-card" className="bg-[var(--background-secondary)] rounded-3xl shadow p-6">
          <div id="card-header" className="mb-4 text-center">
            <h2 className="text-3xl font-bold text-[var(--text-primary)]">
              {t('highlight')}
            </h2>
            <p className="text-lg text-[var(--text-secondary)]">
              {t('hDescription')}
            </p>
          </div>

          <div id="highlightContainer" className="mb-4">
            {/* Loading state */}
            {highlightLoading && (
              <div className="highlight-loading flex flex-col items-center justify-center p-10 text-center" style={{ minHeight: '250px' }}>
                <div className="loading-spinner text-4xl text-red-600 mb-4">
                  <i className="fas fa-circle-notch fa-spin"></i>
                </div>
                <p className="text-[var(--text-secondary)]">Carregando destaques...</p>
              </div>
            )}

            {/* Error state */}
            {highlightError && (
              <div className="highlight-error flex flex-col items-center justify-center p-10 text-center min-h-[250px]">
                <div className="error-icon text-4xl text-red-600 mb-4">
                  <i className="fas fa-exclamation-triangle"></i>
                </div>
                <p id="error-message" className="text-[var(--text-secondary)] mb-6">{highlightError}</p>
                <button
                  id="retry-highlights"
                  className="retry-btn bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors flex items-center"
                  onClick={retryHighlights}
                >
                  <i className="fas fa-sync-alt mr-2"></i> Tentar novamente
                </button>
              </div>
            )}

            {/* Highlight content */}
            {highlightPlaylist && !highlightLoading && !highlightError && (
              <div className="highlight-wrapper w-full rounded-lg shadow">
                <div className="video-container relative aspect-video rounded-t-lg">
                  {showHighlightVideo ? (
                    <iframe
                      id="highlight-iframe"
                      src={highlightPlaylist.url}
                      title="Featured Playlist"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      referrerPolicy="strict-origin-when-cross-origin"
                      allowFullScreen
                      loading="lazy"
                      className="absolute top-0 left-0 w-full h-full rounded-t-lg"
                    />
                  ) : (
                    <div className="placeholder-content absolute top-0 left-0 w-full h-full flex flex-col items-center justify-center bg-black/80 text-white p-5 text-center">
                      <div className="placeholder-icon text-5xl text-red-600 mb-4">
                        <i className="fab fa-youtube"></i>
                      </div>
                      <p className="mb-6 text-lg">{highlightPlaylist.title}</p>
                      <button
                        className="load-video-btn bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-full transition-all duration-300 transform hover:scale-105 flex items-center"
                        onClick={() => setShowHighlightVideo(true)}
                      >
                        <i className="fas fa-play mr-2"></i>
                        {t('loadVideo')}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="button-container text-center">
            <button
              aria-label="Check Out YouTube Playlist"
              type="button"
              className="inline-flex items-center px-5 py-2.5 w-full justify-center text-sm font-medium text-center text-white bg-red-700 hover:bg-red-800 dark:bg-red-600 dark:hover:bg-red-700 rounded-lg focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800"
            >
              <a href="https://www.youtube.com/playlist?list=OLAK5uy_laLvEldekJ_qsP5DMbG-PYcEW3oQEYu_Q" target="_blank" rel="noopener noreferrer" className="flex items-center w-full justify-center">
                <i className="fab fa-youtube mr-2"></i>
                <p className="text-lg font-medium">{t('checkOut')}</p>
              </a>
            </button>
          </div>
        </div>
      </section>

      {/* Social Proof Section - matching original template structure */}
      <section id="social-proof" className="pb-5" ref={statsRef}>
        <div id="statsContainer" className="flex flex-row justify-center flex-wrap gap-2.5">
          <div className="stats-card text-center transition duration-300 ease-in-out bg-[var(--background-secondary)] text-[var(--text-primary)] my-auto p-8 rounded-3xl shadow-lg lg:max-w-70 md:max-w-50 w-full">
            <i className="fas fa-music text-red-600 text-5xl"></i>
            <h3
              className="counter text-4xl font-bold my-4"
              data-target="30"
              data-suffix="+"
              data-duration="1500"
            >
              0
            </h3>
            <p>{t('tracksCreated')}</p>
          </div>
          <div className="stats-card text-center transition duration-300 ease-in-out bg-[var(--background-secondary)] text-[var(--text-primary)] my-auto p-8 rounded-3xl shadow-lg lg:max-w-70 md:max-w-50 w-full">
            <i className="fas fa-users text-red-600 text-5xl"></i>
            <h3
              className="counter text-4xl font-bold my-4"
              data-target="300"
              data-suffix="+"
              data-duration="2000"
              data-delay="200"
            >
              0
            </h3>
            <p>{t('monthlyListeners')}</p>
          </div>
          <div className="stats-card text-center transition duration-300 ease-in-out bg-[var(--background-secondary)] text-[var(--text-primary)] my-auto p-8 rounded-3xl shadow-lg lg:max-w-70 md:max-w-50 w-full">
            <i className="fas fa-star text-red-600 text-5xl"></i>
            <h3
              className="counter text-4xl font-bold my-4"
              data-target="5.0"
              data-decimals="1"
              data-duration="1800"
              data-delay="400"
            >
              0.0
            </h3>
            <p>{t('averageRating')}</p>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
