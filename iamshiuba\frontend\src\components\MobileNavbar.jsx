import { useState } from 'react'
import { Link } from 'react-router-dom'
import { useTranslation } from '../hooks/useTranslation.jsx'

const MobileNavbar = () => {
  const [menuDrawerOpen, setMenuDrawerOpen] = useState(false)
  const [socialDrawerOpen, setSocialDrawerOpen] = useState(false)
  const [configDrawerOpen, setConfigDrawerOpen] = useState(false)
  const { t, changeLanguage, currentLanguage } = useTranslation()

  const toggleMenuDrawer = () => {
    setMenuDrawerOpen(!menuDrawerOpen)
    setSocialDrawerOpen(false)
    setConfigDrawerOpen(false)
  }

  const toggleSocialDrawer = () => {
    setSocialDrawerOpen(!socialDrawerOpen)
    setMenuDrawerOpen(false)
    setConfigDrawerOpen(false)
  }

  const toggleConfigDrawer = () => {
    setConfigDrawerOpen(!configDrawerOpen)
    setMenuDrawerOpen(false)
    setSocialDrawerOpen(false)
  }

  const closeAllDrawers = () => {
    setMenuDrawerOpen(false)
    setSocialDrawerOpen(false)
    setConfigDrawerOpen(false)
  }

  const handleThemeChange = (theme) => {
    document.documentElement.setAttribute('data-theme', theme)
    localStorage.setItem('theme', theme)
  }

  const languages = [
    { code: 'en-US', name: 'English', flag: 'fi-us' },
    { code: 'pt-BR', name: 'Português', flag: 'fi-br' },
    { code: 'jp-JP', name: '日本語', flag: 'fi-jp' },
    { code: 'ru-RU', name: 'Русский', flag: 'fi-ru' },
    { code: 'hi-IN', name: 'हिन्दी', flag: 'fi-in' },
    { code: 'zh-CN', name: '中文', flag: 'fi-cn' },
  ]

  return (
    <div id="m-NavContainer">
      <nav>
        <div className="menu-grid">
          <Link to="/" className="nav-item" aria-label="Início">
            <i className="fas fa-home"></i>
          </Link>

          <button
            onClick={toggleMenuDrawer}
            className="nav-item"
          >
            <i className="fas fa-compass"></i>
          </button>

          <button
            onClick={toggleSocialDrawer}
            className="nav-item"
          >
            <i className="fas fa-share-nodes"></i>
          </button>

          <button
            onClick={toggleConfigDrawer}
            className="nav-item"
          >
            <i className="fas fa-sliders"></i>
          </button>
        </div>
      </nav>

      {/* Menu Drawer */}
      <div
        id="menu-drawer"
        className={`fixed top-0 left-0 z-[51] h-screen p-4 overflow-y-auto transition-transform bg-[var(--background-secondary)] w-80 ${
          menuDrawerOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        tabIndex="-1"
        aria-labelledby="drawer-label"
      >
        <div className="drawer-header">
          <h1 id="drawer-label">Menu</h1>
          <button
            type="button"
            onClick={closeAllDrawers}
            className="drawer-close"
            aria-controls="menu-drawer"
          >
            <i className="fas fa-xmark"></i>
          </button>
        </div>
        <div className="drawer-content">
          <ul>
            <li>
              <Link to="/" className="drawer-link" onClick={closeAllDrawers}>
                <i className="fas fa-home"></i>
                <span data-translate="Homepage">{t('Homepage')}</span>
              </Link>
            </li>
            <li>
              <Link to="/streaming" className="drawer-link" onClick={closeAllDrawers}>
                <i className="fas fa-play"></i>
                <span data-translate="Streaming">{t('Streaming')}</span>
              </Link>
            </li>
            <li>
              <Link to="/about" className="drawer-link" onClick={closeAllDrawers}>
                <i className="fas fa-info-circle"></i>
                <span data-translate="About">{t('About')}</span>
              </Link>
            </li>
            <li>
              <Link to="/terms" className="drawer-link" onClick={closeAllDrawers}>
                <i className="fas fa-file-contract"></i>
                <span data-translate="tos">{t('tos')}</span>
              </Link>
            </li>
            <li>
              <Link to="/privacy" className="drawer-link" onClick={closeAllDrawers}>
                <i className="fas fa-shield-halved"></i>
                <span data-translate="privacy">{t('privacy')}</span>
              </Link>
            </li>
            <li>
              <Link to="/updates" className="drawer-link" onClick={closeAllDrawers}>
                <i className="fas fa-bell"></i>
                <span data-translate="updates">{t('updates')}</span>
              </Link>
            </li>
          </ul>
        </div>
      </div>

      {/* Social Drawer */}
      <div
        id="social-drawer"
        className={`fixed top-0 left-0 z-[51] h-screen p-4 overflow-y-auto transition-transform bg-[var(--background-secondary)] w-80 ${
          socialDrawerOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        tabIndex="-1"
        aria-labelledby="drawer-label"
      >
        <div className="drawer-header">
          <h1 data-translate="followMe">{t('followMe')}</h1>
          <button
            type="button"
            onClick={closeAllDrawers}
            aria-controls="social-drawer"
            className="drawer-close"
          >
            <i className="fas fa-xmark"></i>
          </button>
        </div>
        <div className="drawer-content">
          <div id="social-icons">
            <a
              href="https://twitter.com/iamshiuba"
              target="_blank"
              rel="noopener"
              aria-label="Twitter"
              className="fa-brands fa-x-twitter"
            >
            </a>
            <a
              href="https://soundcloud.com/iamshiuba"
              target="_blank"
              rel="noopener"
              aria-label="SoundCloud"
              className="fa-brands fa-soundcloud"
            >
            </a>
            <a
              href="https://youtube.com/@iamshiuba"
              target="_blank"
              rel="noopener"
              aria-label="YouTube"
              className="fa-brands fa-youtube"
            >
            </a>
          </div>
        </div>
      </div>

      {/* Config Drawer */}
      <div
        id="config-drawer"
        className={`fixed top-0 right-0 z-[51] h-screen p-4 overflow-y-auto transition-transform bg-[var(--background-secondary)] w-80 ${
          configDrawerOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        tabIndex="-1"
        aria-labelledby="drawer-label"
      >
        <div className="drawer-header">
          <h1 data-translate="config">{t('config')}</h1>
          <button
            type="button"
            onClick={closeAllDrawers}
            aria-controls="config-drawer"
            className="drawer-close"
          >
            <i className="fas fa-xmark"></i>
          </button>
        </div>
        <div className="drawer-content">
          <div id="theme-container">
            <h5><span data-translate="theme">{t('theme')}</span></h5>
            <div className="flex justify-start w-20">
              <button
                onClick={() => handleThemeChange('light')}
                title="Light"
                aria-label="Light"
                className="theme-button mr-2.5"
              >
                <i className="fas fa-sun"></i>
              </button>
              <button
                onClick={() => handleThemeChange('dark')}
                title="Dark"
                aria-label="Dark"
                className="theme-button mr-2.5"
              >
                <i className="fas fa-moon"></i>
              </button>
              <button
                onClick={() => handleThemeChange('black')}
                title="Black"
                aria-label="Black"
                className="theme-button mr-2.5"
              >
                <i className="fas fa-lightbulb"></i>
              </button>
              <button
                onClick={() => handleThemeChange('red')}
                title="Red"
                aria-label="Red"
                className="theme-button"
              >
                <i className="fas fa-heart"></i>
              </button>
            </div>
          </div>
          <div id="language-container">
            <h5 data-translate="Translations">{t('Translations')}</h5>
            <ul id="language" aria-labelledby="language-label" role="group">
              {languages.map((lang) => (
                <li key={lang.code} className="langItem">
                  <a
                    onClick={() => changeLanguage(lang.code)}
                    title={lang.name}
                    className={`fi ${lang.flag} ${currentLanguage === lang.code ? 'active' : ''}`}
                    aria-label={lang.name}
                    style={{ cursor: 'pointer' }}
                  >
                    <p>{lang.code}</p>
                  </a>
                </li>
              ))}
            </ul>
          </div>
          <h5 lang="pt-BR">Versão: <b className="text-sm">v3.2.22</b></h5>
        </div>
      </div>
    </div>
  )
}

export default MobileNavbar
